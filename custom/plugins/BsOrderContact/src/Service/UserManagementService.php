<?php declare(strict_types=1);

namespace Bs\OrderContact\Service;

use Bs\OrderContact\Core\Content\ContactPerson\ContactPersonEntity;
use Shopware\Core\Checkout\Customer\CustomerEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Uuid\Uuid;

/**
 * Class UserManagementService
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class UserManagementService
{
    private EntityRepository $contactPersonRepository;
    private EntityRepository $customerRepository;

    public function __construct(
        EntityRepository $contactPersonRepository,
        EntityRepository $customerRepository
    ) {
        $this->contactPersonRepository = $contactPersonRepository;
        $this->customerRepository = $customerRepository;
    }

    /**
     * Get all customers with the same customer number as the given customer
     */
    public function getCustomersByCustomerNumber(string $customerNumber, Context $context): array
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('customerNumber', $customerNumber));
        
        $customers = $this->customerRepository->search($criteria, $context);
        
        $result = [];
        /** @var CustomerEntity $customer */
        foreach ($customers->getElements() as $customer) {
            $result[] = [
                'id' => $customer->getId(),
                'email' => $customer->getEmail(),
                'firstName' => $customer->getFirstName(),
                'lastName' => $customer->getLastName(),
                'customerNumber' => $customer->getCustomerNumber(),
            ];
        }
        
        return $result;
    }

    /**
     * Get contact persons for a specific customer
     */
    public function getContactPersonsForCustomer(string $customerId, Context $context): array
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('customerId', $customerId));

        $contactPersons = $this->contactPersonRepository->search($criteria, $context);

        $result = [];
        /** @var ContactPersonEntity $contactPerson */
        foreach ($contactPersons->getElements() as $contactPerson) {
            $result[] = [
                'id' => $contactPerson->getId(),
                'contactPerson' => $contactPerson->getName(),
            ];
        }

        return $result;
    }

    /**
     * Add a contact person for a specific customer
     */
    public function addContactPerson(string $customerId, string $contactPersonName, Context $context): bool
    {
        try {
            $data = [
                'id' => Uuid::randomHex(),
                'customerId' => $customerId,
                'name' => trim($contactPersonName),
            ];

            $this->contactPersonRepository->create([$data], $context);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remove a contact person by ID
     */
    public function removeContactPerson(string $contactPersonId, Context $context): bool
    {
        try {
            $this->contactPersonRepository->delete([['id' => $contactPersonId]], $context);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if a customer is an order manager
     */
    public function isOrderManager(CustomerEntity $customer): bool
    {
        $customFields = $customer->getCustomFields() ?? [];
        return array_key_exists('order_manager', $customFields) && $customFields['order_manager'] === true;
    }

    /**
     * Get contact persons for checkout (for a specific customer)
     */
    public function getContactPersonsForCheckout(string $customerId, Context $context): array
    {
        return $this->getContactPersonsForCustomer($customerId, $context);
    }
}
