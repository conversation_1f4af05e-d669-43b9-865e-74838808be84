{% sw_extends '@Storefront/storefront/page/account/sidebar.html.twig' %}

{% block page_account_sidebar_menu_inner %}
    {{ parent() }}
    {% block page_account_sidebar_link_bs_belege1 %}
        <a href="{{ path('frontend.account.user.management') }}"
           title="{{ 'bs.orderContact.userManagement.title'|trans|sw_sanitize  }}"
           class="list-group-item list-group-item-action account-aside-item{% if activeRoute is same as('frontend.account.user.management') %} is-active{% endif %}">
            {{ 'bs.orderContact.userManagement.title'|trans|sw_sanitize  }}
        </a>
    {% endblock %}
{% endblock %}