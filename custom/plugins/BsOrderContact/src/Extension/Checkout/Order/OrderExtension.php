<?php declare(strict_types=1);

namespace Bs\OrderContact\Extension\Checkout\Order;

use Bs\OrderContact\Core\Content\ContactPerson\ContactPersonDefinition;
use Shopware\Core\Checkout\Order\OrderDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityExtension;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

/**
 * Class OrderExtension
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class OrderExtension extends EntityExtension
{
    public function extendFields(FieldCollection $collection): void
    {
        // Add association to contact person entity for easier querying
        $collection->add(
            new ManyToOneAssociationField(
                'bsContactPerson',
                'custom_fields.bs_contact_person_id',
                ContactPersonDefinition::class,
                'id',
                false
            )
        );
    }

    public function getDefinitionClass(): string
    {
        return OrderDefinition::class;
    }
}
