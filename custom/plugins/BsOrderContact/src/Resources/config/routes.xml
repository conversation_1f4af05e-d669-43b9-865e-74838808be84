<?xml version="1.0" encoding="UTF-8" ?>
<routes xmlns="http://symfony.com/schema/routing"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://symfony.com/schema/routing
        https://symfony.com/schema/routing/routing-1.0.xsd">

    <!-- User Management Routes -->
    <route id="frontend.account.user.management" path="/account/user-management" methods="GET">
        <default key="_controller">Bs\OrderContact\Storefront\Controller\UserManagementController::userManagementPage</default>
        <default key="_routeScope">["storefront"]</default>
    </route>

    <route id="frontend.account.user.management.add.contact" path="/account/user-management/add-contact" methods="POST">
        <default key="_controller">Bs\OrderContact\Storefront\Controller\UserManagementController::addContactPerson</default>
        <default key="_routeScope">["storefront"]</default>
        <option key="seo">false</option>
    </route>

    <route id="frontend.account.user.management.remove.contact" path="/account/user-management/remove-contact" methods="POST">
        <default key="_controller">Bs\OrderContact\Storefront\Controller\UserManagementController::removeContactPerson</default>
        <default key="_routeScope">["storefront"]</default>
        <option key="seo">false</option>
    </route>

    <!-- Checkout Contact Person Route -->
    <route id="frontend.checkout.contact.persons" path="/checkout/contact-persons" methods="GET">
        <default key="_controller">Bs\OrderContact\Storefront\Controller\UserManagementController::getContactPersonsForCheckout</default>
        <default key="_routeScope">["storefront"]</default>
        <option key="seo">false</option>
    </route>
</routes>
