<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <!-- Entity Definition -->
        <service id="Bs\OrderContact\Core\Content\ContactPerson\ContactPersonDefinition">
            <tag name="shopware.entity.definition" entity="bs_order_contact_person"/>
        </service>

        <!-- User Management Service -->
        <service id="Bs\OrderContact\Service\UserManagementService">
            <argument type="service" id="bs_order_contact_person.repository"/>
            <argument type="service" id="customer.repository"/>
        </service>

        <!-- Controllers -->
        <service id="Bs\OrderContact\Storefront\Controller\UserManagementController" public="true">
            <argument type="service" id="Bs\OrderContact\Service\UserManagementService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <!-- Subscribers -->
        <service id="Bs\OrderContact\Subscriber\CheckoutSubscriber">
            <argument type="service" id="Bs\OrderContact\Service\UserManagementService"/>
            <argument type="service" id="order.repository"/>
            <argument type="service" id="bs_order_contact_person.repository"/>
            <argument type="service" id="request_stack"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Bs\OrderContact\Subscriber\CheckoutPageSubscriber">
            <argument type="service" id="Bs\OrderContact\Service\UserManagementService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <!-- Order Extension -->
        <service id="Bs\OrderContact\Extension\Checkout\Order\OrderExtension">
            <tag name="shopware.entity.extension"/>
        </service>

        <!-- Account Menu Extension -->
        <service id="Bs\OrderContact\Subscriber\AccountMenuSubscriber">
            <argument type="service" id="Bs\OrderContact\Service\UserManagementService"/>
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container>
