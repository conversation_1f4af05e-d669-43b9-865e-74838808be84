<?php declare(strict_types=1);

namespace Bs\OrderContact\Storefront\Controller;

use Bs\OrderContact\Service\UserManagementService;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class UserManagementController
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 * 
 * @RouteScope(scopes={"storefront"})
 */
class UserManagementController extends StorefrontController
{
    private UserManagementService $userManagementService;

    public function __construct(UserManagementService $userManagementService)
    {
        $this->userManagementService = $userManagementService;
    }

    /**
     * @Route("/account/user-management", name="frontend.account.user.management", methods={"GET"})
     */
    public function userManagementPage(Request $request, SalesChannelContext $context): Response
    {
        $customer = $context->getCustomer();
        
        if (!$customer) {
            return $this->redirectToRoute('frontend.account.login.page');
        }

        // Check if customer is an order manager
        if (!$this->userManagementService->isOrderManager($customer)) {
            $this->addFlash('danger', 'You do not have permission to access user management.');
            return $this->redirectToRoute('frontend.account.home.page');
        }

        // Get all customers with the same customer number
        $customers = $this->userManagementService->getCustomersByCustomerNumber(
            $customer->getCustomerNumber(),
            $context->getContext()
        );

        // Get contact persons for each customer
        $customersWithContacts = [];
        foreach ($customers as $customerData) {
            $contactPersons = $this->userManagementService->getContactPersonsForCustomer(
                $customerData['id'],
                $context->getContext()
            );

            $customerData['contactPersons'] = $contactPersons;
            $customersWithContacts[] = $customerData;
        }

        return $this->renderStorefront('@BsOrderContact/storefront/page/account/user-management.html.twig', [
            'customers' => $customersWithContacts,
            'currentCustomer' => $customer,
        ]);
    }

    /**
     * @Route("/account/user-management/add-contact", name="frontend.account.user.management.add.contact", methods={"POST"}, options={"seo"="false"})
     */
    public function addContactPerson(Request $request, SalesChannelContext $context): JsonResponse
    {
        $customer = $context->getCustomer();
        
        if (!$customer || !$this->userManagementService->isOrderManager($customer)) {
            return new JsonResponse(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $customerId = $request->request->get('customerId');
        $contactPersonName = $request->request->get('contactPerson');

        if (empty($customerId) || empty($contactPersonName)) {
            return new JsonResponse(['success' => false, 'message' => 'Customer ID and contact person name are required']);
        }

        $success = $this->userManagementService->addContactPerson(
            $customerId,
            $contactPersonName,
            $context->getContext()
        );

        if ($success) {
            return new JsonResponse(['success' => true, 'message' => 'Contact person added successfully']);
        }

        return new JsonResponse(['success' => false, 'message' => 'Failed to add contact person. It may already exist.']);
    }

    /**
     * @Route("/account/user-management/remove-contact", name="frontend.account.user.management.remove.contact", methods={"POST"}, options={"seo"="false"})
     */
    public function removeContactPerson(Request $request, SalesChannelContext $context): JsonResponse
    {
        $customer = $context->getCustomer();
        
        if (!$customer || !$this->userManagementService->isOrderManager($customer)) {
            return new JsonResponse(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $contactPersonId = $request->request->get('contactPersonId');

        if (empty($contactPersonId)) {
            return new JsonResponse(['success' => false, 'message' => 'Contact person ID is required']);
        }

        $success = $this->userManagementService->removeContactPerson(
            $contactPersonId,
            $context->getContext()
        );

        if ($success) {
            return new JsonResponse(['success' => true, 'message' => 'Contact person removed successfully']);
        }

        return new JsonResponse(['success' => false, 'message' => 'Failed to remove contact person']);
    }

    /**
     * @Route("/checkout/contact-persons", name="frontend.checkout.contact.persons", methods={"GET"}, options={"seo"="false"})
     */
    public function getContactPersonsForCheckout(Request $request, SalesChannelContext $context): JsonResponse
    {
        $customer = $context->getCustomer();
        
        if (!$customer) {
            return new JsonResponse(['contactPersons' => []]);
        }

        $contactPersons = $this->userManagementService->getContactPersonsForCheckout(
            $customer->getId(),
            $context->getContext()
        );

        return new JsonResponse(['contactPersons' => $contactPersons]);
    }
}
