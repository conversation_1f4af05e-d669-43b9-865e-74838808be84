<?php declare(strict_types=1);

namespace Bs\OrderContact;

use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Shopware\Core\Kernel;

/**
 * Class BsOrderContact
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BsOrderContact extends Plugin
{
    /**
     * @param UninstallContext $uninstallContext
     * @return void
     * @throws Exception
     */
    public function uninstall(UninstallContext $uninstallContext): void
    {
        parent::uninstall($uninstallContext);

        if ($uninstallContext->keepUserData()) {
            return;
        }

        $connection = Kernel::getConnection();
        $connection->executeStatement('DROP TABLE IF EXISTS `bs_order_contact_person`');

    }
}
