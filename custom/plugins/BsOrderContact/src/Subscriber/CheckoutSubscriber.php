<?php declare(strict_types=1);

namespace Bs\OrderContact\Subscriber;

use Bs\OrderContact\Service\UserManagementService;
use Shopware\Core\Checkout\Order\OrderEvents;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Class CheckoutSubscriber
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class CheckoutSubscriber implements EventSubscriberInterface
{
    private UserManagementService $userManagementService;
    private EntityRepository $orderRepository;
    private EntityRepository $contactPersonRepository;
    private RequestStack $requestStack;

    public function __construct(
        UserManagementService $userManagementService,
        EntityRepository $orderRepository,
        EntityRepository $contactPersonRepository,
        RequestStack $requestStack
    ) {
        $this->userManagementService = $userManagementService;
        $this->orderRepository = $orderRepository;
        $this->contactPersonRepository = $contactPersonRepository;
        $this->requestStack = $requestStack;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OrderEvents::ORDER_WRITTEN_EVENT => 'onOrderWritten',
            CheckoutConfirmPageLoadedEvent::class => 'onCheckoutConfirmPageLoaded',
        ];
    }

    /**
     * Handle order written event to save contact person information
     */
    public function onOrderWritten(EntityWrittenEvent $event): void
    {
        $request = $this->requestStack->getCurrentRequest();

        if (!$request) {
            return;
        }

        // Get contact person from request (could be from form data or session)
        $contactPersonId = $request->request->get('contactPerson')
            ?? $request->getSession()->get('bs_contact_person_id');

        if (!$contactPersonId) {
            return;
        }

        // Get the contact person details
        $criteria = new Criteria([$contactPersonId]);
        $contactPerson = $this->contactPersonRepository->search($criteria, $event->getContext())->first();

        if (!$contactPerson) {
            return;
        }

        // Update the orders with contact person information in custom fields
        $orderUpdates = [];
        foreach ($event->getIds() as $orderId) {
            $orderUpdates[] = [
                'id' => $orderId,
                'customFields' => [
                    'bs_contact_person_id' => $contactPersonId,
                    'bs_contact_person_name' => $contactPerson->getName(),
                ],
            ];
        }

        if (!empty($orderUpdates)) {
            $this->orderRepository->update($orderUpdates, $event->getContext());
        }

        // Clear the session data
        $request->getSession()->remove('bs_contact_person_id');
    }

    /**
     * Handle checkout confirm page loaded event to store contact person in session
     */
    public function onCheckoutConfirmPageLoaded(CheckoutConfirmPageLoadedEvent $event): void
    {
        $request = $event->getRequest();
        $contactPersonId = $request->request->get('contactPerson');

        if ($contactPersonId) {
            // Store contact person ID in session for later use during order creation
            $request->getSession()->set('bs_contact_person_id', $contactPersonId);
        }
    }
}
