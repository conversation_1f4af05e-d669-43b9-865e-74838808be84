{% sw_extends '@Storefront/storefront/page/checkout/confirm/confirm-address.html.twig' %}

{% block page_checkout_confirm_address_billing_address %}
    {{ parent() }}

    {% if page.cart.lineItems.count > 0 and page.extensions.bsContactPersons|length > 0 %}
        <div class="checkout-contact-person-selection mt-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">{{ "checkout.contactPerson.label"|trans|sw_sanitize }}</h5>
                    <div class="form-group">
                        <select class="form-control" id="contact-person-select" name="contact<PERSON><PERSON>" required>
                            <option value="">{{ "checkout.contactPerson.placeholder"|trans|sw_sanitize }}</option>
                            {% for contact in page.extensions.bsContactPersons %}
                                <option value="{{ contact.id }}">{{ contact.contactPerson }}</option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            {{ "checkout.contactPerson.required"|trans|sw_sanitize }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block base_body_script %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const contactPersonSelect = document.getElementById('contact-person-select');

            if (contactPersonSelect) {
                // Add form validation
                const form = contactPersonSelect.closest('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        if (contactPersonSelect.hasAttribute('required') && !contactPersonSelect.value) {
                            e.preventDefault();
                            contactPersonSelect.classList.add('is-invalid');
                            contactPersonSelect.focus();
                            return false;
                        }
                        contactPersonSelect.classList.remove('is-invalid');
                    });

                    // Remove invalid class on change
                    contactPersonSelect.addEventListener('change', function() {
                        this.classList.remove('is-invalid');
                    });
                }
            }
        });
    </script>
{% endblock %}
