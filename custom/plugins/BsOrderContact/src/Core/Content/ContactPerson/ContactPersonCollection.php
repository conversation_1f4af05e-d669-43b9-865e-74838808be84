<?php declare(strict_types=1);

namespace Bs\OrderContact\Core\Content\ContactPerson;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * Class ContactPersonCollection
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 * 
 * @method void                     add(ContactPersonEntity $entity)
 * @method void                     set(string $key, ContactPersonEntity $entity)
 * @method ContactPersonEntity[]    getIterator()
 * @method ContactPersonEntity[]    getElements()
 * @method ContactPersonEntity|null get(string $key)
 * @method ContactPersonEntity|null first()
 * @method ContactPersonEntity|null last()
 */
class ContactPersonCollection extends EntityCollection
{
    protected function getExpectedClass(): string
    {
        return ContactPersonEntity::class;
    }
}
