/*! For license information please see brainst-customer-input.js.LICENSE.txt */
!function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/bundles/brainstcustomerinput/",n(n.s="vwcf")}({"2fPG":function(e,t,n){var i=n("vwNK");i.__esModule&&(i=i.default),"string"==typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);(0,n("SZ7m").default)("230d6e32",i,!0,{})},Ajpr:function(e){e.exports=JSON.parse('{"customer-input":{"general":{"title":"Brainst Customer Inputs","description":"Manage Customer Inputs"},"list":{"title":"Input List","add":"Add Input","empty-message":"No Data Found","table":{"customer-number":"Customer Number","active":"Active","created-at":"Created At","updated-at":"Updated At"}},"create":{"title":"Create Input","language-description":"New Customer Input"},"form":{"customer-number":{"label":"Customer Number","placeholder":"Select a customer number"},"active":{"label":"Active"},"field1":{"label":"Field1 values","placeholder":"option text","value":"Value","isRequiredLabel":"Is field1 required?","defaultLabel":"Field1 default value"},"field2":{"label":"Field2 values","placeholder":"option text","value":"Value","isRequiredLabel":"Is field2 required?","defaultLabel":"Field2 default value"},"field3":{"label":"Field3 values","placeholder":"option text","value":"Value","isRequiredLabel":"Is field3 required?","defaultLabel":"Field3 default value"},"field4":{"isRequiredLabel":"Is field4 required?"}},"details":{"save":"Save","cancel":"Cancel","title":"Customer Input details","general-title":"General Details","input-title":"Input Details"}}}')},BElz:function(e){e.exports=JSON.parse('{"customer-input":{"general":{"title":"Brainst-Kundeneingaben","description":"Kundeneingaben verwalten"},"list":{"title":"Eingabeliste","add":"Eingabe hinzufügen","empty-message":"Keine Daten gefunden","table":{"customer-number":"Kundennummer","active":"Aktiv","created-at":"Erstellt am","updated-at":"Aktualisiert am"}},"create":{"title":"Eingabe erstellen","language-description":"Neukundeneingabe"},"form":{"customer-number":{"label":"Kundennummer","placeholder":"Wählen Sie eine Kundennummer aus"},"active":{"label":"Aktiv"},"field1":{"label":"Feld1-Werte","placeholder":"Optionstext","value":"Wert","isRequiredLabel":"Ist Feld1 erforderlich?","defaultLabel":"Standardwert für Feld1"},"field2":{"label":"Feld2-Werte","placeholder":"Optionstext","value":"Wert","isRequiredLabel":"Ist Feld2 erforderlich?","defaultLabel":"Standardwert für Feld2"},"field3":{"label":"Field3-Werte","placeholder":"Optionstext","value":"Wert","isRequiredLabel":"Ist Feld3 erforderlich?","defaultLabel":"Standardwert für Feld3"},"field4":{"isRequiredLabel":"Ist Feld4 erforderlich?"}},"details":{"save":"Speichern","cancel":"Stornieren","title":"Details zur Kundeneingabe","general-title":"Allgemeine Details","input-title":"Eingabedetails"}}}')},SZ7m:function(e,t,n){"use strict";function i(e,t){for(var n=[],i={},r=0;r<t.length;r++){var o=t[r],s=o[0],u={id:e+":"+r,css:o[1],media:o[2],sourceMap:o[3]};i[s]?i[s].parts.push(u):n.push(i[s]={id:s,parts:[u]})}return n}n.r(t),n.d(t,"default",(function(){return f}));var r="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!r)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},s=r&&(document.head||document.getElementsByTagName("head")[0]),u=null,a=0,l=!1,c=function(){},d=null,p="data-vue-ssr-id",m="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function f(e,t,n,r){l=n,d=r||{};var s=i(e,t);return b(s),function(t){for(var n=[],r=0;r<s.length;r++){var u=s[r];(a=o[u.id]).refs--,n.push(a)}t?b(s=i(e,t)):s=[];for(r=0;r<n.length;r++){var a;if(0===(a=n[r]).refs){for(var l=0;l<a.parts.length;l++)a.parts[l]();delete o[a.id]}}}}function b(e){for(var t=0;t<e.length;t++){var n=e[t],i=o[n.id];if(i){i.refs++;for(var r=0;r<i.parts.length;r++)i.parts[r](n.parts[r]);for(;r<n.parts.length;r++)i.parts.push(v(n.parts[r]));i.parts.length>n.parts.length&&(i.parts.length=n.parts.length)}else{var s=[];for(r=0;r<n.parts.length;r++)s.push(v(n.parts[r]));o[n.id]={id:n.id,refs:1,parts:s}}}}function _(){var e=document.createElement("style");return e.type="text/css",s.appendChild(e),e}function v(e){var t,n,i=document.querySelector("style["+p+'~="'+e.id+'"]');if(i){if(l)return c;i.parentNode.removeChild(i)}if(m){var r=a++;i=u||(u=_()),t=w.bind(null,i,r,!1),n=w.bind(null,i,r,!0)}else i=_(),t=y.bind(null,i),n=function(){i.parentNode.removeChild(i)};return t(e),function(i){if(i){if(i.css===e.css&&i.media===e.media&&i.sourceMap===e.sourceMap)return;t(e=i)}else n()}}var h,g=(h=[],function(e,t){return h[e]=t,h.filter(Boolean).join("\n")});function w(e,t,n,i){var r=n?"":i.css;if(e.styleSheet)e.styleSheet.cssText=g(t,r);else{var o=document.createTextNode(r),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(o,s[t]):e.appendChild(o)}}function y(e,t){var n=t.css,i=t.media,r=t.sourceMap;if(i&&e.setAttribute("media",i),d.ssrId&&e.setAttribute(p,t.id),r&&(n+="\n/*# sourceURL="+r.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},hoLJ:function(e,t,n){var i=n("q7iY");i.__esModule&&(i=i.default),"string"==typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);(0,n("SZ7m").default)("0f38c678",i,!0,{})},q7iY:function(e,t,n){},vwNK:function(e,t,n){},vwcf:function(e,t,n){"use strict";n.r(t);var i=Shopware.Data.Criteria;Shopware.Component.register("brainst-customer-input-list",{template:'\n{% block brainst_customer_input_list %}\n    \n    {% block brainst_customer_input_list %}\n        <sw-page class="sas-blog-list">\n\n            <template #smart-bar-header>\n                <h2>\n                    \n                    {% block brainst_customer_input_list_smart_bar_header_title_text %}\n                        {{ $tc(\'customer-input.list.title\') }}\n                    {% endblock %}\n                    \n                    {% block brainst_customer_input_list_smart_bar_header_amount %}\n                        <span v-if="!isLoading" class="sw-page__smart-bar-amount">\n                            ({{ total }})\n                        </span>\n                    {% endblock %}\n                </h2>\n            </template>\n\n            <template #language-switch>\n                <sw-language-switch @on-change="changeLanguage"></sw-language-switch>\n            </template>\n\n            \n            {% block brainst_customer_input_list_smart_bar_actions %}\n                <template #smart-bar-actions>\n                    \n                    {% block brainst_customer_input_list_smart_bar_actions_add %}\n                        <sw-button :routerLink="{ name: \'brainst.customer.input.create\' }" variant="primary">\n                            {{ $tc(\'customer-input.list.add\') }}\n                        </sw-button>\n                    {% endblock %}\n                </template>\n            {% endblock %}\n\n            <template #content>\n                \n                {% block brainst_customer_input_list_content %}\n                    <sw-entity-listing\n                            v-if="customerInputEntries"\n                            :items="customerInputEntries"\n                            :repository="customerInputEntriesRepository"\n                            :showSelection="false"\n                            :columns="columns"\n                            showSelection\n                            detailRoute="brainst.customer.input.detail"\n                            @update-records="updateRecords($event)"\n                    >\n                    </sw-entity-listing>\n                {% endblock %}\n\n                \n                {% block brainst_customer_input_list_empty_state %}\n                    <sw-empty-state v-if="!isLoading && !total" :title="$tc(\'customer-input.list.empty-message\')">\n                        {{ $tc(\'customer-input.list.empty-message\') }}\n                    </sw-empty-state>\n                {% endblock %}\n            </template>\n            \n        </sw-page>\n    {% endblock %}\n\n{% endblock %}',inject:["repositoryFactory"],data:function(){return{customerInputEntries:null,page:1,limit:25,total:0,isLoading:!0}},metaInfo:function(){return{title:this.$createTitle()}},created:function(){this.getList()},computed:{customerInputEntriesRepository:function(){return this.repositoryFactory.create("brainst_customer_input")},columns:function(){return[{primary:!0,allowResize:!0,dataIndex:"customerNumber",label:this.$tc("customer-input.list.table.customer-number"),property:"customerNumber",routerLink:"brainst.customer.input.detail"},{property:"active",label:this.$tc("customer-input.list.table.active"),align:"center",allowResize:!0,dataIndex:"active",inlineEdit:"boolean"},{property:"createdAt",label:this.$tc("customer-input.list.table.created-at"),align:"center",allowResize:!0,dataIndex:"createdAt"},{property:"updatedAt",label:this.$tc("customer-input.list.table.updated-at"),align:"center",allowResize:!0,dataIndex:"updatedAt"}]}},methods:{changeLanguage:function(){this.getList()},updateRecords:function(e){this.total=e.total},getList:function(){var e=this;this.isLoading=!0;var t=new i(this.page,this.limit);return t.addFields("customerNumber","active","createdAt","updatedAt"),t.addSorting(i.sort("createdAt","DESC",!1)),this.customerInputEntriesRepository.search(t,Shopware.Context.api).then((function(t){e.customerInputEntries=t,e.isLoading=!1,e.updateRecords(t)}))}}});n("2fPG");function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(){o=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",a=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var o=t&&t.prototype instanceof f?t:f,s=Object.create(o.prototype),u=new O(r||[]);return i(s,"_invoke",{value:I(e,n,u)}),s}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=d;var m={};function f(){}function b(){}function _(){}var v={};c(v,u,(function(){return this}));var h=Object.getPrototypeOf,g=h&&h(h(S([])));g&&g!==t&&n.call(g,u)&&(v=g);var w=_.prototype=f.prototype=Object.create(v);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function o(i,s,u,a){var l=p(e[i],e,s);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==r(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,u,a)}),(function(e){o("throw",e,u,a)})):t.resolve(d).then((function(e){c.value=e,u(c)}),(function(e){return o("throw",e,u,a)}))}a(l.arg)}var s;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){o(e,n,t,i)}))}return s=s?s.then(i,i):i()}})}function I(e,t,n){var i="suspendedStart";return function(r,o){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===r)throw o;return E()}for(n.method=r,n.arg=o;;){var s=n.delegate;if(s){var u=k(s,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===i)throw i="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i="executing";var a=p(e,t,n);if("normal"===a.type){if(i=n.done?"completed":"suspendedYield",a.arg===m)continue;return{value:a.arg,done:n.done}}"throw"===a.type&&(i="completed",n.method="throw",n.arg=a.arg)}}}function k(e,t){var n=t.method,i=e.iterator[n];if(void 0===i)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var r=p(i,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,m;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,m):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function F(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(F,this),this.reset(!0)}function S(e){if(e){var t=e[u];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,r=function t(){for(;++i<e.length;)if(n.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return b.prototype=_,i(w,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:b,configurable:!0}),b.displayName=c(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,c(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},y(x.prototype),c(x.prototype,a,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,i,r,o){void 0===o&&(o=Promise);var s=new x(d(t,n,i,r),o);return e.isGeneratorFunction(n)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},y(w),c(w,l,"Generator"),c(w,u,(function(){return this})),c(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var i in t)n.push(i);return n.reverse(),function e(){for(;n.length;){var i=n.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},e.values=S,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(n,i){return s.type="throw",s.arg=e,t.next=n,i&&(t.method="next",t.arg=void 0),!!i}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),a=n.call(o,"finallyLoc");if(u&&a){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!a)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),L(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var i=n.completion;if("throw"===i.type){var r=i.arg;L(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:S(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),m}},e}function s(e,t,n,i,r,o,s){try{var u=e[o](s),a=u.value}catch(e){return void n(e)}u.done?t(a):Promise.resolve(a).then(i,r)}function u(e){return function(){var t=this,n=arguments;return new Promise((function(i,r){var o=e.apply(t,n);function u(e){s(o,i,r,u,a,"next",e)}function a(e){s(o,i,r,u,a,"throw",e)}u(void 0)}))}}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function l(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===r(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var c=Shopware,d=c.Context,p=c.Component,m=Shopware.Data.Criteria,f=p.getComponentHelper().mapPropertyErrors;p.register("brainst-customer-input-detail",{template:'<sw-page class="sas-blog-list">\n    <template #smart-bar-header>\n        <h2>\n            \n            {% block brainst_customer_input_detail_smart_bar_header_title_text %}\n                {{ $tc(\'customer-input.details.title\') }}\n            {% endblock %}\n        </h2>\n    </template>\n    <template #language-switch>\n        <sw-language-switch @on-change="changeLanguage"\n                            :abort-change-function="abortOnLanguageChange"\n                            :disabled="!brainstCustomerInputId"></sw-language-switch>\n    </template>\n\n    \n    {% block brainst_customer_input_detail_smart_bar_actions %}\n\n        <template #smart-bar-actions>\n            \n            {% block brainst_customer_input_detail_smart_bar_actions %}\n                <sw-button class="sw-tooltip--wrapper" @click="saveCustomerInput" variant="primary">\n                    <sw-loader v-if="isLoading" size="25px">{{ $tc(\'customer-input.details.save\') }}</sw-loader>\n                    <sw-icon v-else-if="isSaved" name="regular-checkmark-xs"\n                             size="15px">{{ $tc(\'customer-input.details.save\') }}</sw-icon>\n                    <span v-else>{{ $tc(\'customer-input.details.save\') }}</span>\n                </sw-button>\n            {% endblock %}\n        </template>\n\n    {% endblock %}\n\n    <template #content>\n        \n        {% block brainst_customer_input_detail_content %}\n            <sw-card-view>\n                \n                {% block brainst_customer_input_detail_general_card %}\n                    <sw-card :title="$tc(\'customer-input.details.general-title\')"\n                             positionIdentifier="brainst-customer-input-general">\n\n                        \n                        {% block brainst_customer_input_detail_customer_number %}\n                            <sw-entity-single-select\n                                    v-model="customerNumber"\n                                    show-clearable-button\n                                    label-property="customerNumber"\n                                    value-property="customerNumber"\n                                    entity="customer"\n                                    :criteria="customerCriteria()"\n                                    :label="$tc(\'customer-input.form.customer-number.label\')"\n                                    :placeholder="$tc(\'customer-input.form.customer-number.placeholder\')"\n                                    :error="customerInputCustomerNumberError"\n                                    @option-select="optionSelect"\n                            ></sw-entity-single-select>\n                        {% endblock %}\n\n                        \n                        {% block brainst_customer_input_detail_active %}\n                            <sw-switch-field\n                                    v-model="customerInput.active"\n                                    :label="$tc(\'customer-input.form.active.label\')"\n                                    :error="customerInputActiveError">\n                            </sw-switch-field>\n                        {% endblock %}\n                    </sw-card>\n                {% endblock %}\n\n                \n                {% block brainst_customer_input_detail_input_card %}\n                    <sw-card :title="$tc(\'customer-input.details.input-title\')"\n                             positionIdentifier="brainst-customer-input-input">\n                        <sw-container columns="repeat(auto-fit, minmax(200px, 1fr))" gap="10px">\n                            <div class="field-1-extra">\n                                <sw-checkbox-field\n                                        v-model="customerInput.customFields.input1IsRequired"\n                                        :label="$tc(\'customer-input.form.field1.isRequiredLabel\')">\n                                </sw-checkbox-field>\n                                <sw-select-field\n                                        v-model="customerInput.customFields.input1Default"\n                                        :label="$tc(\'customer-input.form.field1.defaultLabel\')">\n                                    <option value=""></option>\n                                    <option\n                                            v-for="field1DefaultOption in customerInput.customFields.input1"\n                                            :key="field1DefaultOption.value"\n                                            :value="JSON.stringify(field1DefaultOption)">\n                                        {{ field1DefaultOption.value + \'-\' + field1DefaultOption.description }}\n                                    </option>\n                                </sw-select-field>\n                            </div>\n                            <div class="field-2-extra">\n                                <sw-checkbox-field\n                                        v-model="customerInput.customFields.input2IsRequired"\n                                        :label="$tc(\'customer-input.form.field2.isRequiredLabel\')">\n                                </sw-checkbox-field>\n                                <sw-select-field\n                                        v-model="customerInput.customFields.input2Default"\n                                        :label="$tc(\'customer-input.form.field2.defaultLabel\')">\n                                    <option value=""></option>\n                                    <option\n                                            v-for="field2DefaultOption in customerInput.customFields.input2"\n                                            :key="field2DefaultOption.value"\n                                            :value="JSON.stringify(field2DefaultOption)">\n                                        {{ field2DefaultOption.value + \'-\' + field2DefaultOption.description }}\n                                    </option>\n                                </sw-select-field>\n                            </div>\n                            <div class="field-3-extra">\n                                <sw-checkbox-field\n                                        v-model="customerInput.customFields.input3IsRequired"\n                                        :label="$tc(\'customer-input.form.field3.isRequiredLabel\')">\n                                </sw-checkbox-field>\n                                <sw-select-field\n                                        v-model="customerInput.customFields.input3Default"\n                                        :label="$tc(\'customer-input.form.field3.defaultLabel\')">\n                                    <option value=""></option>\n                                    <option\n                                            v-for="field3DefaultOption in customerInput.customFields.input3"\n                                            :key="field3DefaultOption.value"\n                                            :value="JSON.stringify(field3DefaultOption)">\n                                        {{ field3DefaultOption.value + \'-\' + field3DefaultOption.description }}\n                                    </option>\n                                </sw-select-field>\n                            </div>\n                            <div class="field-4-extra">\n                                <sw-checkbox-field\n                                        v-model="customerInput.customFields.input4IsRequired"\n                                        :label="$tc(\'customer-input.form.field4.isRequiredLabel\')">\n                                </sw-checkbox-field>\n                            </div>\n                        </sw-container>\n                        <sw-container columns="repeat(auto-fit, minmax(250px, 1fr))" gap="10px">\n                            \n                            {% block brainst_sliding_banner_detail_input1 %}\n                                <div class="brainst-customer-input-group">\n                                    <template v-for="(input, index) in customerInput.customFields.input1">\n                                        <sw-text-field\n                                                :key="\'description1\' + index"\n                                                v-model.trim="customerInput.customFields.input1[index].description"\n                                                class="brainst-customer-input-group--input"\n                                                :label="index === 0 ? $tc(\'customer-input.form.field1.label\') : \'\'"\n                                                :placeholder="$tc(\'customer-input.form.field1.placeholder\')"\n                                        >\n                                            <template #prefix>\n                                                <input\n                                                        :key="\'value1\' + index"\n                                                        v-model.trim="customerInput.customFields.input1[index].value"\n                                                        id="sw-field--customerInput-customFields-input1[index]-value"\n                                                        type="text"\n                                                        class="brainst-customer-input-group--prefix"\n                                                        name="sw-field--customerInput-customFields-input1[index]-value"\n                                                        :placeholder="$tc(\'customer-input.form.field1.value\')"\n                                                >\n                                            </template>\n                                            <template v-if="index" #suffix>\n                                                <svg\n                                                        xmlns="http://www.w3.org/2000/svg"\n                                                        viewBox="0 0 24 24"\n                                                        width="24px"\n                                                        height="24px"\n                                                        fill="red"\n                                                        @click="removeInput(\'input1\', index)"\n                                                        class="brainst-customer-input-clear-icon"\n                                                        aria-label="Remove field"\n                                                >\n                                                    <path d="M18.3 5.71L12 12.01 5.7 5.7 4.29 7.11l6.3 6.3-6.3 6.29 1.42 1.41 6.29-6.29 6.29 6.3 1.42-1.42-6.3-6.3 6.3-6.29z"/>\n                                                </svg>\n                                            </template>\n                                        </sw-text-field>\n                                    </template>\n                                    <button class="sw-button sw-button--primary" @click="addField(\'1\')">Add\n                                    </button>\n                                </div>\n                            {% endblock %}\n\n                            \n                            {% block brainst_sliding_banner_detail_input2 %}\n                                <div class="brainst-customer-input-group">\n                                    <template v-for="(input, index) in customerInput.customFields.input2">\n                                        <sw-text-field\n                                                :key="\'description2\' + index"\n                                                v-model.trim="customerInput.customFields.input2[index].description"\n                                                class="brainst-customer-input-group--input"\n                                                :label="index === 0 ? $tc(\'customer-input.form.field2.label\') : \'\'"\n                                                :placeholder="$tc(\'customer-input.form.field2.placeholder\')"\n                                                :hasPrefix="true"\n                                        >\n                                            <template #prefix>\n                                                <input\n                                                        :key="\'value2\' + index"\n                                                        v-model.trim="customerInput.customFields.input2[index].value"\n                                                        id="sw-field--customerInput-customFields-input2[index]-value"\n                                                        type="text"\n                                                        class="brainst-customer-input-group--prefix"\n                                                        name="sw-field--customerInput-customFields-input2[index]-value"\n                                                        :placeholder="$tc(\'customer-input.form.field2.value\')"\n                                                >\n                                            </template>\n                                            <template v-if="index" #suffix>\n                                                <svg\n                                                        xmlns="http://www.w3.org/2000/svg"\n                                                        viewBox="0 0 24 24"\n                                                        width="24px"\n                                                        height="24px"\n                                                        fill="red"\n                                                        @click="removeInput(\'input2\', index)"\n                                                        class="brainst-customer-input-clear-icon"\n                                                        aria-label="Remove field"\n                                                >\n                                                    <path d="M18.3 5.71L12 12.01 5.7 5.7 4.29 7.11l6.3 6.3-6.3 6.29 1.42 1.41 6.29-6.29 6.29 6.3 1.42-1.42-6.3-6.3 6.3-6.29z"/>\n                                                </svg>\n                                            </template>\n                                        </sw-text-field>\n                                    </template>\n                                    <button class="sw-button sw-button--primary" @click="addField(\'2\')">Add\n                                    </button>\n                                </div>\n                            {% endblock %}\n\n                            \n                            {% block brainst_sliding_banner_detail_input3 %}\n                                <div class="brainst-customer-input-group">\n                                    <template v-for="(input, index) in customerInput.customFields.input3">\n                                        <sw-text-field\n                                                :key="\'description3\' + index"\n                                                v-model.trim="customerInput.customFields.input3[index].description"\n                                                class="brainst-customer-input-group--input"\n                                                :label="index === 0 ? $tc(\'customer-input.form.field3.label\') : \'\'"\n                                                :placeholder="$tc(\'customer-input.form.field3.placeholder\')"\n                                                :hasPrefix="true"\n                                        >\n                                            <template #prefix>\n                                                <input\n                                                        :key="\'value3\' + index"\n                                                        v-model.trim="customerInput.customFields.input3[index].value"\n                                                        id="sw-field--customerInput-customFields-input3[index]-value"\n                                                        type="text"\n                                                        class="brainst-customer-input-group--prefix"\n                                                        name="sw-field--customerInput-customFields-input3[index]-value"\n                                                        :placeholder="$tc(\'customer-input.form.field3.value\')"\n                                                >\n                                            </template>\n\n                                            <template v-if="index" #suffix>\n                                                <svg\n                                                        xmlns="http://www.w3.org/2000/svg"\n                                                        viewBox="0 0 24 24"\n                                                        width="24px"\n                                                        height="24px"\n                                                        fill="red"\n                                                        @click="removeInput(\'input3\', index)"\n                                                        class="brainst-customer-input-clear-icon"\n                                                        aria-label="Remove field"\n                                                >\n                                                    <path d="M18.3 5.71L12 12.01 5.7 5.7 4.29 7.11l6.3 6.3-6.3 6.29 1.42 1.41 6.29-6.29 6.29 6.3 1.42-1.42-6.3-6.3 6.3-6.29z"/>\n                                                </svg>\n                                            </template>\n                                        </sw-text-field>\n                                    </template>\n                                    <button class="sw-button sw-button--primary" @click="addField(\'3\')">Add\n                                    </button>\n                                </div>\n                            {% endblock %}\n                        </sw-container>\n                    </sw-card>\n                {% endblock %}\n            </sw-card-view>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["acl","repositoryFactory"],data:function(){return{isLoading:!1,brainstCustomerInputId:null,customerNumber:"",selectedItem:null,customerInput:{customerNumber:"",active:!0,customFields:{input1IsRequired:!1,input2IsRequired:!1,input3IsRequired:!1,input4IsRequired:!1,input1Default:"",input2Default:"",input3Default:"",input1:[{description:"",value:""}],input2:[{description:"",value:""}],input3:[{description:"",value:""}]}},currentLanguageId:d.api.languageId,isSaved:!1}},computed:function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({customerRepository:function(){return this.repositoryFactory.create("customer")},customerInputRepository:function(){return this.repositoryFactory.create("brainst_customer_input")},isCreateMode:function(){return"brainst.customer.input.create"===this.$route.name}},f("customerInput",["customerNumber","active"])),created:function(){var e=this;return u(o().mark((function t(){var n;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.$route.params.id){t.next=6;break}return e.brainstCustomerInputId=e.$route.params.id,t.next=4,e.loadCustomerInput();case 4:t.next=10;break;case 6:d.api.languageId=d.api.systemLanguageId,n=e.customerInputRepository.create(d.api),Object.assign(n,e.customerInput),e.customerInput=n;case 10:case"end":return t.stop()}}),t)})))()},methods:{initializeCustomerInput:function(){return{customerNumber:"",active:!0}},customerCriteria:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=new m(1,25);return e&&t.addFilter(m.equals("customerNumber",e)),t.addGroupField("customerNumber"),t},changeLanguage:function(e){var t=this;return u(o().mark((function n(){return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return t.currentLanguageId=e,n.next=3,t.loadCustomerInput();case 3:case"end":return n.stop()}}),n)})))()},loadCustomerInput:function(){var e=this;return u(o().mark((function t(){var n;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.customerInputRepository.get(e.brainstCustomerInputId,d.api);case 2:if(!(n=t.sent)){t.next=7;break}return e.customerInput=n,t.next=7,e.customerRepository.search(e.customerCriteria(e.customerInput.customerNumber),d.api).then((function(t){t.total>0&&(e.customerNumber=t[0].id)})).catch((function(e){console.error("Error searching for customer:",e)}));case 7:case"end":return t.stop()}}),t)})))()},optionSelect:function(e,t){this.selectedItem=t},saveCustomerInput:function(){var e=this;return u(o().mark((function t(){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.setCustomerNumber(),e.cleanInputFields(),e.isLoading=!0,t.prev=3,t.next=6,e.customerInputRepository.save(e.customerInput,d.api);case 6:if(e.brainstCustomerInputId){t.next=10;break}e.$router.push({name:"brainst.customer.input.detail",params:{id:e.customerInput.id}}),t.next=15;break;case 10:return t.next=12,e.loadCustomerInput();case 12:e.isLoading=!1,e.isSaved=!0,setTimeout((function(){e.isSaved=!1}),2e3);case 15:t.next=21;break;case 17:t.prev=17,t.t0=t.catch(3),e.isLoading=!1,console.error("Error saving customer input:",t.t0);case 21:case"end":return t.stop()}}),t,null,[[3,17]])})))()},setCustomerNumber:function(){this.customerNumber&&this.selectedItem?this.customerInput.customerNumber=this.selectedItem.customerNumber:this.customerNumber||(this.customerInput.customerNumber="")},cleanInputFields:function(){var e=this,t=this.customerInput.customFields,n=["input1IsRequired","input2IsRequired","input3IsRequired","input4IsRequired","input1Default","input2Default","input3Default"];Object.keys(t).forEach((function(i){if(!n.includes(i)){var r=t[i].filter(Boolean);e.customerInput.customFields[i]=r.length?r:[""]}}))},abortOnLanguageChange:function(){return this.customerInputRepository.hasChanges(this.customerInputRepository.create(d.api))},addField:function(e){this.customerInput.customFields["input"+e].push({description:"",value:""})},removeInput:function(e,t){this.customerInput.customFields[e].splice(t,1)}}});Shopware.Component.extend("brainst-customer-input-create","brainst-customer-input-detail",{template:"{% block brainst_customer_input_detail_smart_bar_header_title_text %}\n    {{ $tc('customer-input.create.title') }}\n{% endblock %}\n\n{% block brainst_customer_input_detail_language_info %}\n    <sw-language-info :entityDescription=\"$tc('customer-input.create.language-description')\"\n                      :isNewEntity=\"true\">\n    </sw-language-info>\n{% endblock %}"});var b=n("BElz"),_=n("Ajpr");Shopware.Module.register("brainst-customer-input",{type:"plugin",name:"customer-input.general.title",title:"customer-input.general.title",description:"customer-input.general.description",color:"#57D9A3FF",icon:"regular-map",favicon:"icon-module-settings.png",snippets:{"de-DE":b,"en-GB":_},routes:{list:{component:"brainst-customer-input-list",path:"list"},detail:{component:"brainst-customer-input-detail",path:"detail/:id",meta:{parentPath:"brainst.customer.input.list"}},create:{component:"brainst-customer-input-create",path:"create",meta:{parentPath:"brainst.customer.input.list"}}},navigation:[{id:"brainst-customer-input",label:"customer-input.general.title",color:"#ff3d58",path:"brainst.customer.input.list",parent:"sw-customer",icon:"default-shopping-paper-bag-product",position:100}],extensionEntryRoute:{extensionName:"BrainstCustomerInput",route:"brainst.customer.input.list"}});n("hoLJ");Shopware.Component.override("sw-order-line-items-grid",{template:'{% block sw_order_line_items_grid_column_payload_options %}\n    {% parent() %}\n\n    {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input %}\n        <template v-if="item.customFields &&\n          item.customFields !== null &&\n          (item.customFields.brainst_field_1 ||\n          item.customFields.brainst_field_2 ||\n          item.customFields.brainst_field_3 ||\n          item.customFields.brainst_field_4)"\n        >\n            <div class="sw-order-line-items-grid__item-product-customer-inputs">\n                {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_1 %}\n                    <span v-if="item.customFields.brainst_field_1 && item.customFields.brainst_field_1.value && item.customFields.brainst_field_1.description"\n                          class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-1">\n                        &#8226; <i>{{ item.customFields.brainst_field_1.value }} - {{ item.customFields.brainst_field_1.description }}</i>\n                    </span>\n                {% endblock %}\n                {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_2 %}\n                    <span v-if="item.customFields.brainst_field_2 && item.customFields.brainst_field_2.value && item.customFields.brainst_field_2.description"\n                          class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-2">\n                        &#8226; <i>{{ item.customFields.brainst_field_2.value }} - {{ item.customFields.brainst_field_2.description }}</i>\n                    </span>\n                {% endblock %}\n                {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_3 %}\n                    <span v-if="item.customFields.brainst_field_3 && item.customFields.brainst_field_3.value && item.customFields.brainst_field_3.description"\n                          class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-3">\n                        &#8226; <i>{{ item.customFields.brainst_field_3.value }} - {{ item.customFields.brainst_field_3.description }}</i>\n                    </span>\n                {% endblock %}\n                {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_4 %}\n                    <span v-if="item.customFields.brainst_field_4 && item.customFields.brainst_field_4 !== \'\'"\n                          class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-4">\n                        &#8226; <i>{{ item.customFields.brainst_field_4 }}</i>\n                    </span>\n                {% endblock %}\n            </div>\n        </template>\n    {% endblock %}\n{% endblock %}\n\n{% block sw_order_line_items_grid_grid_columns_label_content %}\n    <template v-else>\n        {% block sw_order_line_items_grid_grid_columns_label_content_brainst_customer_input %}\n            <template v-if="item.customFields &&\n          item.customFields !== null &&\n          (item.customFields.brainst_field_1 ||\n          item.customFields.brainst_field_2 ||\n          item.customFields.brainst_field_3 ||\n          item.customFields.brainst_field_4)"\n            >\n                <div class="sw-order-line-items-grid__item-product-customer-inputs">\n                    {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_1 %}\n                        <span v-if="item.customFields.brainst_field_1 && item.customFields.brainst_field_1.value && item.customFields.brainst_field_1.description"\n                              class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-1">\n                        &#8226; <i>{{ item.customFields.brainst_field_1.value }} - {{ item.customFields.brainst_field_1.description }}</i>\n                    </span>\n                    {% endblock %}\n                    {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_2 %}\n                        <span v-if="item.customFields.brainst_field_2 && item.customFields.brainst_field_2.value && item.customFields.brainst_field_2.description"\n                              class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-2">\n                        &#8226; <i>{{ item.customFields.brainst_field_2.value }} - {{ item.customFields.brainst_field_2.description }}</i>\n                    </span>\n                    {% endblock %}\n                    {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_3 %}\n                        <span v-if="item.customFields.brainst_field_3 && item.customFields.brainst_field_3.value && item.customFields.brainst_field_3.description"\n                              class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-3">\n                        &#8226; <i>{{ item.customFields.brainst_field_3.value }} - {{ item.customFields.brainst_field_3.description }}</i>\n                    </span>\n                    {% endblock %}\n                    {% block sw_order_line_items_grid_column_payload_options_brainst_customer_input_4 %}\n                        <span v-if="item.customFields.brainst_field_4 && item.customFields.brainst_field_4 !== \'\'"\n                              class="sw-order-line-items-grid__item-product-customer-inputs-input sw-order-line-items-grid__item-product-customer-inputs-input-4">\n                        &#8226; <i>{{ item.customFields.brainst_field_4 }}</i>\n                    </span>\n                    {% endblock %}\n                </div>\n            </template>\n        {% endblock %}\n    </template>\n{% endblock %}\n'})}});
//# sourceMappingURL=brainst-customer-input.js.map